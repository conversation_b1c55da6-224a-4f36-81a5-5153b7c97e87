package com.example.aimusicplayer.di

import android.content.Context
import android.content.SharedPreferences
import com.example.aimusicplayer.api.ApiManager
import com.example.aimusicplayer.api.UnifiedApiService
import com.example.aimusicplayer.data.db.dao.PlayHistoryDao
import com.example.aimusicplayer.data.db.dao.PlaylistDao
import com.example.aimusicplayer.data.db.dao.SongDao
import com.example.aimusicplayer.data.source.ApiService
import com.example.aimusicplayer.data.source.MusicDataSource
import com.example.aimusicplayer.utils.AlbumArtBlurCache
import com.example.aimusicplayer.utils.AlbumArtCache
import com.example.aimusicplayer.utils.Constants
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import javax.inject.Singleton

/**
 * 应用模块
 * 提供应用级别的依赖注入
 */
@Module
@InstallIn(SingletonComponent::class)
object AppModule {

    /**
     * 提供应用上下文
     * 这是为了解决需要注入Context的类（如UserRepository、ApiManager等）
     */
    @Provides
    @Singleton
    fun provideContext(@ApplicationContext context: Context): Context {
        return context
    }

    /**
     * 提供应用级SharedPreferences实例
     */
    @Provides
    @Singleton
    fun provideSharedPreferences(@ApplicationContext context: Context): SharedPreferences {
        return context.getSharedPreferences(Constants.PREF_NAME, Context.MODE_PRIVATE)
    }



    /**
     * 提供UnifiedApiService实例
     */
    @Provides
    @Singleton
    fun provideUnifiedApiService(apiManager: ApiManager): UnifiedApiService {
        return apiManager.apiService
    }

    /**
     * 提供MusicDataSource实例
     */
    @Provides
    @Singleton
    fun provideMusicDataSource(
        @ApplicationContext context: Context,
        okHttpClient: OkHttpClient,
        songDao: SongDao,
        playlistDao: PlaylistDao,
        playHistoryDao: PlayHistoryDao
    ): MusicDataSource {
        return MusicDataSource(context, okHttpClient, songDao, playlistDao, playHistoryDao)
    }

    /**
     * 提供AlbumArtBlurCache实例
     */
    @Provides
    @Singleton
    fun provideAlbumArtBlurCache(@ApplicationContext context: Context): AlbumArtBlurCache {
        return AlbumArtBlurCache(context)
    }

    /**
     * 提供AlbumArtCache实例
     */
    @Provides
    @Singleton
    fun provideAlbumArtCache(@ApplicationContext context: Context): AlbumArtCache {
        return AlbumArtCache.getInstance(context)
    }

    /**
     * 提供PerformanceMonitor实例
     * 用于监控应用性能
     */
    @Provides
    @Singleton
    fun providePerformanceMonitor(@ApplicationContext context: Context): com.example.aimusicplayer.utils.PerformanceMonitor {
        return com.example.aimusicplayer.utils.PerformanceMonitor(context)
    }

    /**
     * 提供FunctionalityTester实例
     * 用于测试应用功能
     */
    @Provides
    @Singleton
    fun provideFunctionalityTester(
        @ApplicationContext context: Context,
        apiManager: ApiManager,
        musicRepository: com.example.aimusicplayer.data.repository.MusicRepository,
        userRepository: com.example.aimusicplayer.data.repository.UserRepository
    ): com.example.aimusicplayer.utils.FunctionalityTester {
        return com.example.aimusicplayer.utils.FunctionalityTester(context, apiManager, musicRepository, userRepository)
    }
}
