package com.example.aimusicplayer.ui.comment

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.aimusicplayer.R
import com.example.aimusicplayer.databinding.FragmentCommentBinding
import com.example.aimusicplayer.ui.adapter.CommentAdapter
import com.example.aimusicplayer.viewmodel.CommentViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * 评论页面
 */
@AndroidEntryPoint
class CommentFragment : Fragment() {

    private var _binding: FragmentCommentBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: CommentViewModel
    private lateinit var adapter: CommentAdapter

    private val TAG = "CommentFragment"

    // 分页加载相关
    private var isLoadingMore = false
    private var hasMoreData = true
    private var currentPage = 0
    private val PAGE_SIZE = 20

    // 获取导航参数
    private val args: CommentFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentCommentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 初始化ViewModel
        viewModel = ViewModelProvider(this)[CommentViewModel::class.java]

        // 设置标题
        binding.textCommentTitle.text = getString(R.string.comment_title, args.songName)

        // 初始化RecyclerView
        setupRecyclerView()

        // 设置下拉刷新
        setupSwipeRefresh()

        // 设置返回按钮
        binding.btnBack.setOnClickListener {
            findNavController().navigateUp()
        }

        // 设置发送按钮
        binding.btnSendComment.setOnClickListener {
            val commentText = binding.editComment.text.toString().trim()
            if (commentText.isNotEmpty()) {
                viewModel.sendComment(args.songId, commentText)
                binding.editComment.text.clear()

                // 隐藏键盘
                val imm = requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                imm.hideSoftInputFromWindow(binding.editComment.windowToken, 0)
            } else {
                Toast.makeText(requireContext(), R.string.comment_empty_hint, Toast.LENGTH_SHORT).show()
            }
        }

        // 观察评论列表
        viewModel.comments.observe(viewLifecycleOwner) { comments ->
            adapter.submitList(comments)
            binding.swipeRefreshLayout.isRefreshing = false

            if (comments.isEmpty()) {
                binding.textEmptyComment.visibility = View.VISIBLE
                binding.recyclerViewComments.visibility = View.GONE
            } else {
                binding.textEmptyComment.visibility = View.GONE
                binding.recyclerViewComments.visibility = View.VISIBLE
            }
        }

        // 观察加载状态
        viewModel.loading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading && !binding.swipeRefreshLayout.isRefreshing)
                View.VISIBLE else View.GONE
        }

        // 观察错误信息
        viewModel.errorMessage.observe(viewLifecycleOwner) { message ->
            if (message.isNotEmpty()) {
                Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
                binding.swipeRefreshLayout.isRefreshing = false
            }
        }

        // 观察评论发送成功事件
        viewModel.commentSent.observe(viewLifecycleOwner) { sent ->
            if (sent) {
                // 显示成功动画
                showCommentSentAnimation()
                // 重置状态
                viewModel.resetCommentSentState()
            }
        }

        // 加载评论
        viewModel.loadComments(args.songId)
    }

    /**
     * 设置下拉刷新
     */
    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.setColorSchemeResources(
            R.color.colorPrimary,
            R.color.colorAccent,
            R.color.colorPrimaryDark
        )

        binding.swipeRefreshLayout.setOnRefreshListener {
            viewModel.loadComments(args.songId)
        }
    }

    /**
     * 显示评论发送成功动画
     * 优化后的版本，添加了更多视觉反馈和触觉反馈
     */
    private fun showCommentSentAnimation() {
        try {
            // 创建一个动画视图容器
            val container = FrameLayout(requireContext())
            container.layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            container.setBackgroundColor(Color.parseColor("#66000000")) // 半透明背景
            container.alpha = 0f

            // 创建成功图标
            val successView = ImageView(requireContext()).apply {
                setImageResource(R.drawable.ic_check_circle)
                setColorFilter(Color.WHITE)
                alpha = 0f
                scaleX = 0.5f
                scaleY = 0.5f
            }

            // 创建成功文本
            val successText = TextView(requireContext()).apply {
                text = "评论发送成功"
                setTextColor(Color.WHITE)
                textSize = 16f
                alpha = 0f
                translationY = 50f
            }

            // 添加到容器中
            container.addView(successView, FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.WRAP_CONTENT,
                FrameLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                gravity = Gravity.CENTER
            })

            container.addView(successText, FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.WRAP_CONTENT,
                FrameLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                gravity = Gravity.CENTER
                topMargin = resources.getDimensionPixelSize(R.dimen.spacing_large)
            })

            // 添加到布局中
            (binding.root as ViewGroup).addView(container)

            // 添加触觉反馈
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                val vibratorManager = requireContext().getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
                val vibrator = vibratorManager.defaultVibrator
                vibrator.vibrate(VibrationEffect.createOneShot(50, VibrationEffect.DEFAULT_AMPLITUDE))
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                @Suppress("DEPRECATION")
                val vibrator = requireContext().getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
                vibrator.vibrate(VibrationEffect.createOneShot(50, VibrationEffect.DEFAULT_AMPLITUDE))
            }

            // 创建动画
            val containerFadeIn = ObjectAnimator.ofFloat(container, "alpha", 0f, 1f)
            val containerFadeOut = ObjectAnimator.ofFloat(container, "alpha", 1f, 0f)

            val iconFadeIn = ObjectAnimator.ofFloat(successView, "alpha", 0f, 1f)
            val iconScaleX = ObjectAnimator.ofFloat(successView, "scaleX", 0.5f, 1.2f, 1f)
            val iconScaleY = ObjectAnimator.ofFloat(successView, "scaleY", 0.5f, 1.2f, 1f)

            val textFadeIn = ObjectAnimator.ofFloat(successText, "alpha", 0f, 1f)
            val textMoveUp = ObjectAnimator.ofFloat(successText, "translationY", 50f, 0f)

            // 设置动画时间
            containerFadeIn.duration = 200
            containerFadeOut.duration = 300
            containerFadeOut.startDelay = 1500

            iconFadeIn.duration = 300
            iconScaleX.duration = 500
            iconScaleY.duration = 500

            textFadeIn.duration = 300
            textFadeIn.startDelay = 200
            textMoveUp.duration = 400
            textMoveUp.startDelay = 200

            // 播放动画
            AnimatorSet().apply {
                play(containerFadeIn)
                play(iconFadeIn).with(iconScaleX).with(iconScaleY).after(containerFadeIn)
                play(textFadeIn).with(textMoveUp).after(iconFadeIn)
                play(containerFadeOut).after(textFadeIn)

                addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        // 动画结束后移除视图
                        (binding.root as ViewGroup).removeView(container)
                    }
                })
                start()
            }
        } catch (e: Exception) {
            Log.e(TAG, "显示评论发送成功动画失败", e)
        }
    }

    private fun setupRecyclerView() {
        adapter = CommentAdapter(
            onLikeClick = { comment ->
                viewModel.likeComment(comment.commentId)
            }
        )
        binding.recyclerViewComments.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = <EMAIL>

            // 添加滚动监听器，实现上拉加载更多
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)

                    val layoutManager = recyclerView.layoutManager as LinearLayoutManager
                    val visibleItemCount = layoutManager.childCount
                    val totalItemCount = layoutManager.itemCount
                    val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()

                    // 检查是否需要加载更多
                    if (!isLoadingMore && hasMoreData) {
                        if ((visibleItemCount + firstVisibleItemPosition) >= totalItemCount
                            && firstVisibleItemPosition >= 0
                            && totalItemCount >= PAGE_SIZE) {

                            // 加载下一页
                            loadMoreComments()
                        }
                    }
                }
            })
        }
    }

    /**
     * 加载更多评论
     * 优化后的版本，添加了加载状态管理和动画效果
     */
    private fun loadMoreComments() {
        if (isLoadingMore || !hasMoreData) return

        isLoadingMore = true
        currentPage++

        // 显示底部加载进度条，添加淡入动画
        binding.loadMoreProgress.apply {
            alpha = 0f
            visibility = View.VISIBLE
            animate()
                .alpha(1f)
                .setDuration(200)
                .start()
        }

        // 观察加载更多状态
        viewModel.isLoadingMore.observe(viewLifecycleOwner) { stillLoading ->
            if (!stillLoading) {
                // 加载完成，隐藏进度条
                binding.loadMoreProgress.animate()
                    .alpha(0f)
                    .setDuration(200)
                    .withEndAction {
                        binding.loadMoreProgress.visibility = View.GONE
                    }
                    .start()

                // 重置加载状态
                isLoadingMore = false

                // 更新是否有更多数据
                viewModel.hasMoreData.observe(viewLifecycleOwner) { hasMore ->
                    hasMoreData = hasMore
                }
            }
        }

        // 加载更多评论
        viewModel.loadMoreComments(args.songId, currentPage, PAGE_SIZE)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
