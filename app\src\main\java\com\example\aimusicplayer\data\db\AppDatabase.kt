package com.example.aimusicplayer.data.db

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.example.aimusicplayer.data.db.converter.DateConverter
import com.example.aimusicplayer.data.db.dao.ApiCacheDao
import com.example.aimusicplayer.data.db.dao.PlayHistoryDao
import com.example.aimusicplayer.data.db.dao.PlaylistDao
import com.example.aimusicplayer.data.db.dao.SongDao
import com.example.aimusicplayer.data.db.dao.UserDao
import com.example.aimusicplayer.data.db.entity.ApiCacheEntity
import com.example.aimusicplayer.data.db.entity.PlayHistoryEntity
import com.example.aimusicplayer.data.db.entity.PlaylistEntity
import com.example.aimusicplayer.data.db.entity.PlaylistSongCrossRef
import com.example.aimusicplayer.data.db.entity.SongEntity
import com.example.aimusicplayer.data.db.entity.UserEntity

/**
 * 应用数据库
 * 集成所有DAO接口
 */
@TypeConverters(DateConverter::class)
@Database(
    entities = [
        SongEntity::class,
        PlaylistEntity::class,
        PlaylistSongCrossRef::class,
        PlayHistoryEntity::class,
        UserEntity::class,
        ApiCacheEntity::class
    ],
    version = 2,
    exportSchema = true
)
abstract class AppDatabase : RoomDatabase() {
    /**
     * 获取歌曲DAO
     */
    abstract fun songDao(): SongDao

    /**
     * 获取歌单DAO
     */
    abstract fun playlistDao(): PlaylistDao

    /**
     * 获取播放历史DAO
     */
    abstract fun playHistoryDao(): PlayHistoryDao

    /**
     * 获取用户DAO
     */
    abstract fun userDao(): UserDao

    /**
     * 获取API缓存DAO
     */
    abstract fun apiCacheDao(): ApiCacheDao

    companion object {
        /**
         * 数据库迁移
         */
        val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(db: SupportSQLiteDatabase) {
                // 创建API缓存表
                db.execSQL(
                    """
                    CREATE TABLE IF NOT EXISTS `api_cache` (
                        `cache_key` TEXT NOT NULL,
                        `data` TEXT NOT NULL,
                        `cache_time` INTEGER NOT NULL,
                        `expiration_time` INTEGER NOT NULL,
                        `cache_type` TEXT NOT NULL,
                        PRIMARY KEY(`cache_key`)
                    )
                    """
                )

                // 添加新字段到播放历史表
                db.execSQL("ALTER TABLE play_history ADD COLUMN is_completed INTEGER NOT NULL DEFAULT 0")
                db.execSQL("ALTER TABLE play_history ADD COLUMN play_source TEXT NOT NULL DEFAULT ''")
                db.execSQL("ALTER TABLE play_history ADD COLUMN user_id TEXT NOT NULL DEFAULT ''")

                // 创建播放时间索引
                db.execSQL("CREATE INDEX IF NOT EXISTS `index_play_history_play_time` ON `play_history` (`play_time`)")
            }
        }
    }
}
