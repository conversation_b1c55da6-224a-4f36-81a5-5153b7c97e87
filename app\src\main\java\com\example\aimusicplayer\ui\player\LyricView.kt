package com.example.aimusicplayer.ui.player

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Typeface
import android.text.TextPaint
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import android.view.animation.LinearInterpolator
import androidx.core.content.ContextCompat
import com.example.aimusicplayer.R
import com.example.aimusicplayer.data.model.LyricLine
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

/**
 * 歌词视图
 * 用于显示歌词并支持滚动
 */
class LyricView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "LyricView"

        // 默认行数
        private const val DEFAULT_LINE_COUNT = 5

        // 默认行高
        private const val DEFAULT_LINE_HEIGHT = 80f

        // 默认字体大小
        private const val DEFAULT_TEXT_SIZE = 40f

        // 默认高亮字体大小
        private const val DEFAULT_HIGHLIGHT_TEXT_SIZE = 45f

        // 默认动画持续时间
        private const val DEFAULT_ANIMATION_DURATION = 300L

        // 默认无歌词文本
        private const val DEFAULT_NO_LYRIC_TEXT = "暂无歌词"

        // 拖动阈值
        private const val DRAG_THRESHOLD = 10
    }

    // 歌词行列表
    private var lyricLines: List<LyricLine> = emptyList()

    // 当前行索引
    private var currentLineIndex = 0

    // 行高
    private var lineHeight = DEFAULT_LINE_HEIGHT

    // 行数
    private var lineCount = DEFAULT_LINE_COUNT

    // 普通文本画笔
    private val normalTextPaint = TextPaint(Paint.ANTI_ALIAS_FLAG).apply {
        color = ContextCompat.getColor(context, R.color.white)
        alpha = 160
        textSize = DEFAULT_TEXT_SIZE
        textAlign = Paint.Align.CENTER
    }

    // 高亮文本画笔
    private val highlightTextPaint = TextPaint(Paint.ANTI_ALIAS_FLAG).apply {
        color = ContextCompat.getColor(context, R.color.white)
        textSize = DEFAULT_HIGHLIGHT_TEXT_SIZE
        textAlign = Paint.Align.CENTER
        typeface = Typeface.DEFAULT_BOLD
    }

    // 翻译文本画笔
    private val translationTextPaint = TextPaint(Paint.ANTI_ALIAS_FLAG).apply {
        color = ContextCompat.getColor(context, R.color.white)
        alpha = 128
        textSize = DEFAULT_TEXT_SIZE * 0.8f
        textAlign = Paint.Align.CENTER
    }

    // 偏移量
    private var offset = 0f

    // 目标偏移量
    private var targetOffset = 0f

    // 滚动动画
    private var scrollAnimator: ValueAnimator? = null

    // 是否正在拖动
    private var isDragging = false

    // 拖动开始Y坐标
    private var dragStartY = 0f

    // 拖动开始偏移量
    private var dragStartOffset = 0f

    // 是否显示翻译
    private var showTranslation = true

    // 手势检测器
    private val gestureDetector = GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {
        override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
            // 单击事件
            if (!isDragging && lyricLines.isNotEmpty()) {
                // 计算点击位置对应的行索引
                val centerY = height / 2f
                val touchY = e.y
                val touchOffset = touchY - centerY
                val touchLineIndex = currentLineIndex + (touchOffset / lineHeight).toInt()

                // 检查索引是否有效
                if (touchLineIndex in lyricLines.indices) {
                    // 通知监听器
                    onLyricClickListener?.invoke(lyricLines[touchLineIndex])
                }
            }
            return true
        }
    })

    // 歌词点击监听器
    var onLyricClickListener: ((LyricLine) -> Unit)? = null

    // 拖动状态变化监听器
    var onDragStateChangeListener: ((Boolean) -> Unit)? = null

    // 拖动位置变化监听器
    var onDragPositionChangeListener: ((LyricLine) -> Unit)? = null

    /**
     * 设置歌词
     * @param lyrics 歌词行列表
     */
    fun setLyrics(lyrics: List<LyricLine>) {
        lyricLines = lyrics
        currentLineIndex = 0
        offset = 0f
        targetOffset = 0f
        invalidate()
    }

    /**
     * 更新当前行
     * @param position 当前播放位置（毫秒）
     * @param smooth 是否平滑滚动
     */
    fun updateCurrentLine(position: Long, smooth: Boolean = true) {
        if (lyricLines.isEmpty()) return

        // 查找当前行
        var index = 0
        for (i in lyricLines.indices) {
            if (i == lyricLines.size - 1 || position < lyricLines[i + 1].time) {
                index = i
                break
            }
        }

        // 如果行索引变化，更新视图
        if (index != currentLineIndex) {
            currentLineIndex = index
            updateOffset(smooth)
        }
    }

    /**
     * 更新偏移量
     * @param smooth 是否平滑滚动
     */
    private fun updateOffset(smooth: Boolean = true) {
        // 计算目标偏移量
        targetOffset = -currentLineIndex * lineHeight

        if (smooth) {
            // 创建滚动动画
            scrollAnimator?.cancel()
            scrollAnimator = ValueAnimator.ofFloat(offset, targetOffset).apply {
                duration = DEFAULT_ANIMATION_DURATION
                interpolator = LinearInterpolator()
                addUpdateListener { animation ->
                    offset = animation.animatedValue as Float
                    invalidate()
                }
                start()
            }
        } else {
            // 直接设置偏移量
            offset = targetOffset
            invalidate()
        }
    }

    /**
     * 设置是否显示翻译
     * @param show 是否显示
     */
    fun setShowTranslation(show: Boolean) {
        showTranslation = show
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val centerX = width / 2f
        val centerY = height / 2f

        if (lyricLines.isEmpty()) {
            // 绘制无歌词提示
            canvas.drawText(DEFAULT_NO_LYRIC_TEXT, centerX, centerY, highlightTextPaint)
            return
        }

        // 计算可见行范围
        val startLine = max(0, currentLineIndex - lineCount)
        val endLine = min(lyricLines.size - 1, currentLineIndex + lineCount)

        // 绘制歌词行
        for (i in startLine..endLine) {
            val line = lyricLines[i]
            val y = centerY + (i * lineHeight) + offset

            // 检查是否在可见范围内
            if (y < -lineHeight || y > height + lineHeight) {
                continue
            }

            // 选择画笔
            val paint = if (i == currentLineIndex) highlightTextPaint else normalTextPaint

            // 绘制歌词文本
            canvas.drawText(line.text, centerX, y, paint)

            // 绘制翻译文本
            if (showTranslation && !line.translation.isNullOrEmpty()) {
                canvas.drawText(line.translation, centerX, y + lineHeight * 0.5f, translationTextPaint)
            }
        }
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        // 先交给手势检测器处理
        if (gestureDetector.onTouchEvent(event)) {
            return true
        }

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                // 取消滚动动画
                scrollAnimator?.cancel()

                // 记录拖动开始位置
                dragStartY = event.y
                dragStartOffset = offset

                return true
            }
            MotionEvent.ACTION_MOVE -> {
                val deltaY = event.y - dragStartY

                // 检查是否达到拖动阈值
                if (!isDragging && abs(deltaY) > DRAG_THRESHOLD) {
                    isDragging = true
                    onDragStateChangeListener?.invoke(true)
                }

                if (isDragging) {
                    // 更新偏移量
                    offset = dragStartOffset + deltaY

                    // 计算当前拖动位置对应的行索引
                    val dragLineIndex = -offset / lineHeight
                    val newLineIndex = dragLineIndex.toInt().coerceIn(0, lyricLines.size - 1)

                    // 如果行索引变化，通知监听器
                    if (newLineIndex != currentLineIndex) {
                        currentLineIndex = newLineIndex
                        onDragPositionChangeListener?.invoke(lyricLines[currentLineIndex])
                    }

                    invalidate()
                }

                return true
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                if (isDragging) {
                    // 结束拖动
                    isDragging = false
                    onDragStateChangeListener?.invoke(false)

                    // 更新偏移量
                    updateOffset()
                }

                return true
            }
        }

        return super.onTouchEvent(event)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        // 根据视图高度调整行高和行数
        lineHeight = h / (lineCount * 2 + 1).toFloat()

        // 更新偏移量
        updateOffset(false)
    }
}
