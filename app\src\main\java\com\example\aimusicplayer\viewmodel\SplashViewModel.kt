package com.example.aimusicplayer.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.data.repository.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * 启动页的ViewModel
 * 负责处理启动页的业务逻辑
 * 使用Flow管理UI状态
 */
@HiltViewModel
class SplashViewModel @Inject constructor(
    application: Application,
    private val userRepository: UserRepository
) : FlowViewModel(application) {

    companion object {
        private const val TAG = "SplashViewModel"
        private const val MIN_SPLASH_TIME = 1500L // 最小启动页显示时间，单位毫秒
    }

    // 启动状态的StateFlow
    private val _splashStateFlow = MutableStateFlow<SplashState>(SplashState.LOADING)
    val splashStateFlow: StateFlow<SplashState> = _splashStateFlow.asStateFlow()
    val splashState: LiveData<SplashState> = splashStateFlow.asLiveData() // 兼容LiveData

    // 是否已登录的StateFlow
    private val _isLoggedInFlow = MutableStateFlow<Boolean>(false)
    val isLoggedInFlow: StateFlow<Boolean> = _isLoggedInFlow.asStateFlow()
    val isLoggedIn: LiveData<Boolean> = isLoggedInFlow.asLiveData() // 兼容LiveData

    // 导航事件的StateFlow
    private val _navigationActionFlow = MutableStateFlow<NavigationAction?>(null)
    val navigationActionFlow: StateFlow<NavigationAction?> = _navigationActionFlow.asStateFlow()

    /**
     * 获取导航事件的LiveData（Java兼容方法）
     * 使用不同的方法名避免与Kotlin属性getter冲突
     */
    fun getNavigationAction(): LiveData<NavigationAction?> = navigationActionFlow.asLiveData()

    // 启动时间
    private var startTime = System.currentTimeMillis()

    init {
        // 初始化启动页
        initSplash()
    }

    /**
     * 初始化启动页
     */
    private fun initSplash() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "初始化启动页失败", e)
                handleError(e, "初始化启动页失败: ${e.message}")
                finishSplash(SplashState.ERROR)
            }
        ) {
            startTime = System.currentTimeMillis()
            _splashStateFlow.value = SplashState.LOADING

            try {
                // 检查登录状态
                val isLoggedIn = withContext(Dispatchers.IO) {
                    userRepository.isLoggedIn()
                }
                _isLoggedInFlow.value = isLoggedIn

                // 确保启动页至少显示MIN_SPLASH_TIME毫秒
                val elapsedTime = System.currentTimeMillis() - startTime
                if (elapsedTime < MIN_SPLASH_TIME) {
                    delay(MIN_SPLASH_TIME - elapsedTime)
                }

                // 完成启动页
                finishSplash(SplashState.FINISHED)
            } catch (e: Exception) {
                Log.e(TAG, "初始化启动页失败", e)
                throw e
            }
        }
    }

    /**
     * 完成启动页
     * @param state 启动状态
     */
    private fun finishSplash(state: SplashState) {
        _splashStateFlow.value = state

        // 如果状态为FINISHED，根据登录状态决定导航方向
        if (state == SplashState.FINISHED) {
            if (_isLoggedInFlow.value) {
                // 用户已登录，导航到主界面
                _navigationActionFlow.value = NavigationAction.NAVIGATE_TO_MAIN
                Log.d(TAG, "用户已登录，导航到主界面")
            } else {
                // 用户未登录，导航到登录界面
                _navigationActionFlow.value = NavigationAction.NAVIGATE_TO_LOGIN
                Log.d(TAG, "用户未登录，导航到登录界面")
            }
        }
    }

    /**
     * 重试初始化
     */
    fun retry() {
        initSplash()
    }

    /**
     * 启动状态枚举
     */
    enum class SplashState {
        LOADING,    // 加载中
        FINISHED,   // 完成
        ERROR       // 错误
    }

    /**
     * 导航动作枚举
     */
    enum class NavigationAction {
        NAVIGATE_TO_LOGIN,  // 导航到登录界面
        NAVIGATE_TO_MAIN    // 导航到主界面
    }
}
